import { Button, Result } from 'antd';
import React from 'react';
import { deleteAuthDetails } from '../util/API/authStorage';

const BlockUser = () => {
    const handleLogout = () => {
        deleteAuthDetails();
        localStorage.removeItem("name");
        localStorage.removeItem("email");
        localStorage.removeItem("token");
        localStorage.removeItem("ItemToken");
        window.location.assign("/");
    };
    return (
        <>
            <Result
                status="403"
                title="Account Blocked"
                subTitle={(
                    <p>
                        Your account has been blocked. For assistance, please contact the Wooffer support team at&nbsp;
                        <a href="mailto:<EMAIL>">
                            <EMAIL>
                        </a>
                        .
                    </p>
                )}
                extra={(
                    <Button
                        type="primary"
                        htmlType="submit"
                        className="mt-5 w-40 h-10  "
                        onClick={handleLogout}
                    >
                        Logout
                    </Button>
                )}
            />
        </>
    );
};
export default BlockUser;
