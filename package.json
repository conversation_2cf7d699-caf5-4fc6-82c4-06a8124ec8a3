{"name": "antd-dashboard", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/plots": "^1.2.5", "@google-pay/button-react": "^3.0.10", "@stripe/react-stripe-js": "^2.7.1", "@stripe/stripe-js": "^3.4.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.9.3", "antd-img-crop": "^4.21.0", "axios": "^1.4.0", "caniuse-lite": "^1.0.30001591", "firebase": "^10.4.0", "jspdf": "^2.5.1", "moment": "^2.29.4", "prismjs": "^1.29.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.4", "react-helmet": "^6.1.0", "react-icons": "^4.11.0", "react-quill": "^2.0.0", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "react-simple-code-editor": "^0.13.1", "reactflow": "^11.10.1", "socket.io-client": "^4.7.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "tailwindcss": "^3.3.2"}}