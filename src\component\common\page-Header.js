import { Col, Row } from "antd";
import React from "react";

import CustomButton from "./Custom-Buttons";

const PageHeader = ({ data }) => {
  const paramas = window.location.pathname;
  return (
    <Col span={24}>
      <Row className="container-head">
        <Col span={24} className="dashboard-headers">
          <Row>
            <p className="dashboard-head capitlize">
              {paramas.split("/").length < 4
                ? paramas.split("/")[2]
                : `${paramas.split("/")[2]} ${data?.name && `|${data?.name}`}`}
            </p>
          </Row>
          <Row>
            {data?.buttons.map((button) => (
              <CustomButton key={button.id} {...button} />
            ))}
          </Row>
        </Col>

      </Row>
    </Col>
  );
};

PageHeader.defaultProps = {
  search: null,
  data: {
    buttons: [],
    name: "",
  },
  details: "",
};

export default PageHeader;
