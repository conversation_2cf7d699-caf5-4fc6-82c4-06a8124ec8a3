import moment from 'moment';

// Dummy Rate Limit Configuration
export const dummyRateLimitConfig = {
  id: 1,
  serviceEnvironmentId: "env-123",
  rateLimit: 1000,
  timeWindow: 1,
  blockDuration: 15,
  permanentBlockThreshold: 10,
  rateLimitMessage: "Rate limit exceeded. Please try again later.",
  ipBlockedMessage: "Your IP has been blocked due to excessive requests.",
  isEnabled: true,
  createdAt: moment().subtract(30, 'days').toISOString(),
  updatedAt: moment().subtract(1, 'day').toISOString(),
};

// Dummy Blocked IPs Data
export const dummyBlockedIPs = [
  {
    id: 1,
    ipAddress: "*************",
    blockType: "temporary",
    reason: "Exceeded rate limit 5 times",
    blockedAt: moment().subtract(2, 'hours').toISOString(),
    expiresAt: moment().add(13, 'hours').toISOString(),
    serviceEnvironmentId: "env-123",
  },
  {
    id: 2,
    ipAddress: "*********",
    blockType: "permanent",
    reason: "Malicious activity detected",
    blockedAt: moment().subtract(1, 'day').toISOString(),
    expiresAt: null,
    serviceEnvironmentId: "env-123",
  },
  {
    id: 3,
    ipAddress: "************",
    blockType: "temporary",
    reason: "Automated bot behavior",
    blockedAt: moment().subtract(30, 'minutes').toISOString(),
    expiresAt: moment().add(14, 'hours', 30, 'minutes').toISOString(),
    serviceEnvironmentId: "env-123",
  },
  {
    id: 4,
    ipAddress: "*************",
    blockType: "permanent",
    reason: "Repeated security violations",
    blockedAt: moment().subtract(3, 'days').toISOString(),
    expiresAt: null,
    serviceEnvironmentId: "env-123",
  },
  {
    id: 5,
    ipAddress: "***********",
    blockType: "temporary",
    reason: "Rate limit exceeded",
    blockedAt: moment().subtract(1, 'hour').toISOString(),
    expiresAt: moment().add(14, 'hours').toISOString(),
    serviceEnvironmentId: "env-123",
  },
];

// Dummy Rate Limit Logs
export const dummyRateLimitLogs = [
  {
    id: 1,
    ipAddress: "*************",
    endpoint: "/api/users",
    requestCount: 1250,
    timeWindow: 1,
    actionTaken: "rate_limited",
    timestamp: moment().subtract(10, 'minutes').toISOString(),
    serviceEnvironmentId: "env-123",
  },
  {
    id: 2,
    ipAddress: "*********",
    endpoint: "/api/orders",
    requestCount: 2000,
    timeWindow: 1,
    actionTaken: "blocked",
    timestamp: moment().subtract(1, 'hour').toISOString(),
    serviceEnvironmentId: "env-123",
  },
  {
    id: 3,
    ipAddress: "************",
    endpoint: "/api/products",
    requestCount: 1500,
    timeWindow: 1,
    actionTaken: "rate_limited",
    timestamp: moment().subtract(2, 'hours').toISOString(),
    serviceEnvironmentId: "env-123",
  },
  {
    id: 4,
    ipAddress: "*************",
    endpoint: "/api/auth/login",
    requestCount: 5000,
    timeWindow: 1,
    actionTaken: "blocked",
    timestamp: moment().subtract(3, 'hours').toISOString(),
    serviceEnvironmentId: "env-123",
  },
  {
    id: 5,
    ipAddress: "***********",
    endpoint: "/api/search",
    requestCount: 1100,
    timeWindow: 1,
    actionTaken: "rate_limited",
    timestamp: moment().subtract(4, 'hours').toISOString(),
    serviceEnvironmentId: "env-123",
  },
  {
    id: 6,
    ipAddress: "*************",
    endpoint: "/api/dashboard",
    requestCount: 1800,
    timeWindow: 1,
    actionTaken: "blocked",
    timestamp: moment().subtract(6, 'hours').toISOString(),
    serviceEnvironmentId: "env-123",
  },
];

// Dummy API Service Functions
export const rateLimitDummyAPI = {
  // Get rate limit configuration
  getRateLimitConfig: (serviceEnvironmentId) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: dummyRateLimitConfig,
        });
      }, 500);
    });
  },

  // Update rate limit configuration
  updateRateLimitConfig: (serviceEnvironmentId, config) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            ...dummyRateLimitConfig,
            ...config,
            updatedAt: moment().toISOString(),
          },
        });
      }, 800);
    });
  },

  // Get blocked IPs
  getBlockedIPs: (serviceEnvironmentId, pagination = {}) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page = 1, limit = 10 } = pagination;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = dummyBlockedIPs.slice(startIndex, endIndex);

        resolve({
          success: true,
          data: {
            rows: paginatedData.map((item, index) => ({
              ...item,
              no: startIndex + index + 1,
              key: item.id,
            })),
            count: dummyBlockedIPs.length,
            page,
            limit,
          },
        });
      }, 600);
    });
  },

  // Block IP
  blockIP: (ipData) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newBlockedIP = {
          id: dummyBlockedIPs.length + 1,
          ...ipData,
          blockedAt: moment().toISOString(),
          expiresAt: ipData.blockType === 'permanent'
            ? null
            : moment().add(15, 'minutes').toISOString(),
        };

        resolve({
          success: true,
          data: newBlockedIP,
        });
      }, 700);
    });
  },

  // Unblock IP
  unblockIP: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: "IP unblocked successfully",
        });
      }, 500);
    });
  },

  // Get rate limit logs
  getRateLimitLogs: (serviceEnvironmentId, pagination = {}) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { page = 1, limit = 10 } = pagination;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedData = dummyRateLimitLogs.slice(startIndex, endIndex);

        resolve({
          success: true,
          data: {
            rows: paginatedData.map((item, index) => ({
              ...item,
              no: startIndex + index + 1,
              key: item.id,
            })),
            count: dummyRateLimitLogs.length,
            page,
            limit,
          },
        });
      }, 600);
    });
  },
};
