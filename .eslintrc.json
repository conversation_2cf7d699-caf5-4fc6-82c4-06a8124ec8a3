{
    "env": {
        "browser": true,
        "es2021": true
    },
    "extends": "airbnb",
    "parserOptions": {
        "ecmaVersion": "latest",
        "sourceType": "module"
    },
    "rules": {
        "linebreak-style":"off",
        "quotes":"off",
        "indent":"off",
        "max-len":"off",
        "camelcase":"off",
        "arrow-body-style":"off",
        "array-callback-return":"off",
        // "no-unused-vars":"off",
        "no-case-declarations":"off",
        "no-unused-expressions":"off",
        "no-nested-ternary":"off",
        "jsx-a11y/click-events-have-key-events":"off",
        "jsx-a11y/no-static-element-interactions":"off",
        "import/no-extraneous-dependencies":"off",
        "import/no-cycle":"off",
        "react/jsx-filename-extension":"off",
        "react/jsx-props-no-spreading":"off",
        "react/destructuring-assignment":"off",
        "react/function-component-definition":"off",
        "react/jsx-indent":"off",
        "react/jsx-indent-props":"off",
        "react/jsx-no-useless-fragment":"off",
        "react/jsx-closing-tag-location":"off",
        "react/prop-types":"off",
        "eslint-disable operator-linebreak":"off"
    }
}
