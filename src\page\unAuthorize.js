import { Button, Result } from "antd";
import React from "react";

const Error = () => {
  return (
    <Result
      status="403"
      title="403"
      subTitle="Sorry, you are not authorized to access this page."
      extra={(
        <Button
        className="textcolor"
          type="primary"
          onClick={() => {
            window.location.assign("/");
          }}
        >
          Back Home
        </Button>
      )}
    />
  );
};

export default Error;
