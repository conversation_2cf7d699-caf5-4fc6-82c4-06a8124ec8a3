import {
  But<PERSON>, <PERSON>, Row, Typography,
} from "antd";
import React from "react";
import { BsStars } from "react-icons/bs";
import { useNavigate } from "react-router-dom";

const { Text } = Typography;
const WarningBar = ({
  isVisible = false,
  setIsWarningVisible = () => { },
}) => {
  const navigate = useNavigate();
  if (!isVisible) return <div />;
  return (
    <Row
      style={{
        borderRadius: "4px",
        // background:
        //   "linear-gradient(90deg, rgb(255 116 61 / 20%) 0%, rgb(255 116 61 / 0%) 135.23%)",
      }}
      className="warning-primary-to-secondary flex rounded-sm justify-between items-center"
    >
      <Col
        span={24}
        lg={14}
        sm="24"
        className="mt-5 gap-2 ms-2 flex items-center justify-center lg:mt-0 lg:justify-start"
      >
        <BsStars className="w-5 h-5 defaultColor" />
        <Text
         className="defaultColor"
          type="primary"
        >
          Upgrade Wooffer and get Unlimited Access to All the Features!

        </Text>
      </Col>
      <Col
        span={24}
        lg={6}
        sm="24"
        className="flex justify-center gap-2 lg:justify-end  items-center "
      >
        <Button
          onClick={() => {
            setIsWarningVisible(false);
          }}
          type="text"
        >
          Not Now
        </Button>
        <Button
          className="m-5 lg:m-1 textcolor"
          type="primary"
          onClick={() => {
            navigate("/app/pricing");
          }}
        >
          Upgrade
        </Button>
      </Col>
    </Row>
  );
};

export default WarningBar;
