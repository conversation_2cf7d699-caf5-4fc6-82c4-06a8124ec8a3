/* eslint-disable no-unused-vars */
import {
    <PERSON><PERSON>, Card, Col, Pagination, Row, Table, Typography,
} from 'antd';
import Search from 'antd/es/input/Search';
import React, { useEffect, useRef, useState } from 'react';
import { CSVLink } from 'react-csv';
import { FaDownload } from 'react-icons/fa';
import moment from 'moment';
import { io } from 'socket.io-client';
import { useParams } from 'react-router-dom';
import useHttp from '../../../../hooks/use-http';
import { analyzeRequests, analyzeRequestsAll } from '../../../../util/functions';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import useIndexedDB from '../../../../hooks/index-db';

const { Text } = Typography;

const socket = io(process.env.REACT_APP_SOCKET_URL);

let totalEndpointCounts = 0;

const ApiUsagePage = (props) => {
    // const requestArray = CONSTANTS.Request.list?.filter(
    //     (el) => el.env === environmentID,
    //   );
    // const { db, addData } = useIndexedDB(
    //     "wooffer",
    //     "wooffer-api-requests",
    // );
    const [apiOverView, setApiOverView] = useState([]);
    const [apiUsageAllData, setApiUsageAllData] = useState([]);
    const [apiUsageDisplayData, setApiUsageDisplayData] = useState([]);

    const [filterText, setFilterText] = useState("");
    const [extraQuery, setExtraQuery] = useState("");
    const [methodQuery, setMethodQuery] = useState("");
    const [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 10,
        total: 0,
    });
    const [refresh, setRefresh] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [searchValue, setSearchValue] = useState("");
    const [allData, setAllData] = useState([]);
    const excelRef = useRef();
    const debounceTimeout = useRef(null);
    const rowSelection = {
        selectedRowKeys,
        onChange: (value) => {
            // console.log(value);
            setSelectedRowKeys(value);
        },

    };
    const { environmentID } = useParams();
    const api = useHttp();
    const handleTableChange = (filters, sorter) => {
        console.log(sorter, filters);
        if (sorter?.method) {
            console.log(sorter?.method?.toString());
            setMethodQuery(`${sorter?.method?.toString()}`);
        } else {
            setMethodQuery("");
        }

        if (sorter?.order) {
            setExtraQuery(
                `&sort=${sorter?.columnKey}&sortBy=${sorter?.order === "descend" ? "DESC" : "ASC"
                }`,
            );
        } else {
            setExtraQuery("");
        }
    };
    // console.log("calling....");
    useEffect(() => {
        // if (db == null) return;
        let currentData = [];
        props.socket.on("requestStart", (systemData) => {
            // console.log(systemData, "respent");

            const EnvironmentID = Object.keys(systemData)[0];
            currentData = [...currentData, { ...systemData[EnvironmentID], env: EnvironmentID }];
            setApiUsageAllData((pr) => [...pr, { ...systemData[EnvironmentID], env: EnvironmentID }]);
            if (currentData?.length > 10) return;
            setApiUsageDisplayData(currentData);
        });
        props.socket.on("responseSent", (res) => {
            console.log(res, "responseSent");
            // const EnvironmentID = Object.keys(systemData)[0];
            // currentData = [...currentData, { ...systemData[EnvironmentID], env: EnvironmentID }];
            // setApiUsageAllData((pr) => [...pr, { ...systemData[EnvironmentID], env: EnvironmentID }]);
            // if (currentData?.length > 10) return;
            // setApiUsageDisplayData(currentData);
        });
    }, []);
    // console.log(apiUsageDisplayData);
    const searchHandler = (value) => {
        // Clear the previous timeout if it exists
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        // Set a new timeout to call the debounced function after a certain delay
        debounceTimeout.current = setTimeout(() => {
            setPagination((prev) => ({ ...prev, currentPage: 1 }));
            setFilterText(value);
        }, 500); // Adjust the delay as needed
    };

    useEffect(() => {
        const filterEnv = apiUsageDisplayData?.filter((el) => el.env === environmentID);
        const resultArray = analyzeRequestsAll(filterEnv || []);

        const methodList = methodQuery ? methodQuery.split(",") : [];
        // console.log(filterEnv, resultArray, CONSTANTS.Request.list);
        const filteredRequests = resultArray?.filter((el) => {
            const matchesMethod = methodList?.length > 0 ? methodList.includes(el.method) : true;
            const matchesSearch = filterText ? el.originalUrl.includes(filterText) : true;
            return matchesMethod && matchesSearch;
        });

        setPagination((prevPagination) => ({
            ...prevPagination,
            total: resultArray?.length ?? 90000,
        }));
        totalEndpointCounts = resultArray?.reduce((sum, ele) => {
            return sum + (ele?.count || 0);
        }, 0);
        setApiOverView(
            filteredRequests?.slice(
                (pagination.currentPage - 1) * pagination.pageSize,
                pagination.currentPage * pagination.pageSize,
            )?.map((ele, i) => {
                return {
                    ...ele,
                    key: ele,
                    no:
                        pagination.currentPage === 1
                            ? i + 1
                            : pagination.pageSize * (pagination.currentPage - 1) + i + 1,
                    method: ele?.method || "-",
                    count: ele?.count || "-",
                    endPointD:
                        <p className="overflow-hidden break-all">{ele?.originalUrl}</p>
                        || "-",
                    requestReceivedTime: ele?.requestReceivedTime
                        ? moment(ele.requestReceivedTime).format("YYYY-MM-DD HH:mm:ss A")
                        : "-",
                };
            }),
        );
    }, [
        filterText,
        extraQuery,
        methodQuery,
        pagination.currentPage,
        pagination.pageSize,
        refresh,
        environmentID,
        apiUsageDisplayData,
    ]);

    useEffect(() => {
        if (allData?.length <= 0) return;
        allData?.length > 0 && excelRef.current.link.click();
        setAllData([]);
    }, [allData]);

    // const exportData = async () => {
    //     const OVERVIEW_API = { ...CONSTANTS.API.overview.getApiCountOverView };
    //     OVERVIEW_API.endpoint = OVERVIEW_API.endpoint.replace(
    //         ":serviceID",
    //         environmentID,
    //     );
    //     OVERVIEW_API.endpoint = `${OVERVIEW_API.endpoint}&isVisible=${0
    //         }`;
    //     if (filterText != null && filterText !== "") {
    //         const query = `&search=${filterText}`;
    //         OVERVIEW_API.endpoint += query;
    //     }
    //     const filterEnv = CONSTANTS.Request.list.filter((el) => el.env === environmentID);
    //     const filteredRequests = await filterEnv?.filter((el) => {
    //         // const matchesMethod = methodList?.length > 0 ? methodList.includes(el.method) : true;
    //         const matchesSearch = filterText ? el.endPoint.includes(filterText) : true;
    //         return matchesSearch;
    //     });

    //     const data = await filteredRequests?.map((ele) => {
    //         return {
    //             endPoint: ele?.endPoint,
    //             method: ele?.method,
    //             requestReceivedTime: ele?.originalUrl,
    //         };
    //     });
    //     await setAllData(data);
    // };

    return (
        <>
            <Card>
                <div className="flex items-center justify-between gap-3">
                    <Text className="hidden text-lg font-semibold  md:block">API usage</Text>
                    <Button
                        className="btn-dashboard-icon textcolor"
                        type="primary"
                        onClick={() => {
                            setApiUsageDisplayData(() => [...apiUsageAllData]);
                        }}
                    >
                        Refresh
                    </Button>
                    {/* {apiOverView?.length > 0 && (
                        <Button
                            className="btn-dashboard-icon textcolor"
                            type="primary"
                            // ghost
                            icon={<FaDownload size={13} />}
                            // size="large"
                            style={{ margin: "0px 5px" }}
                            onClick={exportData}
                        >
                            Export Data
                        </Button>
                    )}
                    <CSVLink className="hidden" ref={excelRef} data={allData} /> */}
                </div>
                <div>
                    Total API Count:
                    {" "}
                    {' '}
                    <b>{totalEndpointCounts}</b>
                </div>

                <div className="mt-3">
                    <Row>
                        <Col span={24} lg={12} xl={8} xxl={8}>
                            <Search
                                value={searchValue}
                                width="auto"
                                onChange={(e) => {
                                    searchHandler(e.target.value);
                                    setSearchValue(e.target.value);
                                }}
                                placeholder="Search by Endpoint name"
                            />
                        </Col>
                    </Row>
                </div>
                <div className="mt-5">
                    <Table
                        dataSource={apiOverView}
                        columns={CONSTANTS.TABLE.API_USAGE_HISTORY}
                        onChange={handleTableChange}
                        pagination={false}
                        scroll={{ x: 800, y: 1300 }}
                    // rowSelection={{
                    //     // type: "checkbox",
                    //     // ...rowSelection,
                    // }}
                    />
                    <Pagination
                        current={pagination?.currentPage}
                        pageSize={pagination?.pageSize}
                        total={pagination?.total}
                        className="mt-10"
                        showSizeChanger
                        onChange={(page, pageSize) => {
                            setPagination((prev) => ({
                                ...prev,
                                currentPage: page,
                                pageSize,
                            }));
                        }}
                    />
                </div>
            </Card>
        </>
    );
};

export default ApiUsagePage;
