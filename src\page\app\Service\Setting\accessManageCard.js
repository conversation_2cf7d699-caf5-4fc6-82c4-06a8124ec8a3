/* eslint-disable react/no-array-index-key */
import {
    Button, Collapse, Row, Select,
} from 'antd';
import React from 'react';
import { TbLockAccess } from 'react-icons/tb';

// const text = `
//   A dog is a type of domesticated animal.
//   Known for its loyalty and faithfulness,
//   it can be found as a welcome guest in many households across the world.
// `;
const accessOptions = [
    {
        label: 'Full Access',
        value: 'full-access',
    },
    {
        label: 'Read Only',
        value: 'read-only',
    },
];

// const itemSecNest = (data) => [
//     {
//         key: '1',
//         label: data?.name,
//         children: text,
//         extra: <Select
//             onClick={(event) => {
//                 // If you don't want click extra trigger collapse, you can prevent this:
//                 event.stopPropagation();
//             }}
//             defaultValue="full-access"
//             // onChange={handleChange}
//             options={accessOptions}
//         />,
//     },
// ];

const itemsNest = (data) => [
    {
        key: data?.name,
        label: data?.name,
        children: data?.serviceEnvironments?.map((serviceData, index) => (
            <div className="p-4 flex justify-between border-b-[1px] border-[#e5e7eb]" key={`child-${index}`}>
                <p>{serviceData?.name}</p>
                {' '}
                <Select
                    defaultValue="full-access"
                    // onChange={handleChange}
                    options={accessOptions}
                />
            </div>
        )),
        extra: <Select
            onClick={(event) => {
                // If you don't want click extra trigger collapse, you can prevent this:
                event.stopPropagation();
            }}
            disabled={false}
            defaultValue="full-access"
            // onChange={handleChange}
            options={accessOptions}
        />,
    },
];

const items = (services) => [
    {
        key: '1',
        label: <div><p>Parthil Dhorajiya</p></div>,
        children: <div>
            {
                services?.map((data, index) => (
                    <Collapse
                        style={{
                            body: {
                                padding: "0px",
                            },
                            content: {
                                padding: "0px",
                            },
                        }}
                        key={`child-${index}`}
                        className="mb-5 services-nested"
                        defaultActiveKey="1"
                        items={itemsNest(data)}
                    />
                ))
            }
            {/* <Collapse className="mb-5" defaultActiveKey="1" items={itemsNest} />
            <Collapse className="mb-5" defaultActiveKey="1" items={itemsNest} /> */}
        </div>,
        extra: <Select
            onClick={(event) => {
                // If you don't want click extra trigger collapse, you can prevent this:
                event.stopPropagation();
            }}
            defaultValue="full-access"
            // onChange={handleChange}
            options={accessOptions}
        />,
    },

];

const AccessManageCard = ({ services, projectDetails, setAddCollobratorDetails }) => {
    const onChange = (key) => {
        console.log(key);
    };
    return (
        <>
            <div className="flex flex-col justify-center items-center">
                <TbLockAccess className="mb-4" size={30} />
                <Row className="mb-1.5 -mt-2 font-medium ">
                    You haven&apos;t invited any collaborators yet.
                </Row>
                <Button
                    className="my-2 px-4 textcolor"
                    type="primary"
                    onClick={() => {
                        setAddCollobratorDetails({
                            name: projectDetails?.name,
                            id: projectDetails?.id,
                            showText: `Invite people to collaborate on this project.`,
                        });
                    }}
                >
                    Add people
                </Button>
            </div>
            <Collapse
                onChange={onChange}
                items={items(services)}
            />
        </>
    );
};

export default AccessManageCard;
