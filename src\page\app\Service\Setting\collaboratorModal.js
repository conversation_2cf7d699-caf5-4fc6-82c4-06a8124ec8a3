/* eslint-disable prefer-promise-reject-errors */
import {
    Button, Col, Form, Input, Modal, Row,
    Select,
} from 'antd';
import React from 'react';
import { FaArrowRightArrowLeft } from 'react-icons/fa6';
import useHttp from '../../../../hooks/use-http';
// import theme from '../../../../theme.json';
import CONSTANTS from '../../../../util/constant/CONSTANTS';

const accessOptions = [
    {
        label: 'Full Access',
        value: 'full-access',
    },
    {
        label: 'Read Only',
        value: 'read-only',
    },
];

const CollaboratorModal = ({ details, setDetails }) => {
    const [form] = Form.useForm();
    // const [isbuttonDisable, setIsbuttonDisable] = useState({
    //     isTextValid: false,
    //     isVerifyTextValid: false,
    // });
    // const [newEmail, setNewEmail] = useState('');
    const api = useHttp();

    const onAccessHandler = () => {
        form?.validateFields().then((values) => {
            const TRANSFER_PROJECT_API = { ...CONSTANTS.API.project.transferOwnership };
            api.sendRequest(TRANSFER_PROJECT_API, (res) => {
                if (res?.status === 'success') {
                    setDetails(null);
                    window.location.assign("/app");
                }
            }, values);
        });
    };

    const footer = {
        footer: [

            // <Popconfirm
            //     onConfirm={() => {
            //         const TRANSFER_PROJECT_API = { ...CONSTANTS.API.project.transferOwnership };
            //         api.sendRequest(TRANSFER_PROJECT_API, (res) => {
            //             if (res?.status === 'success') {
            //                 setDetails(null);
            //                 window.location.assign("/app");
            //             }
            //         }, { email: newEmail, projectId: details?.id });
            //     }}
            //     key="submitConfirm"
            //     title="transfer"
            //     description={(
            //         <p>
            //             The ownership of the project&nbsp;
            //             <span className="font-semibold">{details?.name}</span>
            //             &nbsp;
            //             if transferred to
            //             <span className="ml-1 font-semibold">{newEmail}</span>
            //         </p>
            //     )}
            // >
            <Button
                key="access-submit"
                className="w-full textcolor"
                style={{
                    color: "#fff",
                    // background: theme.token.colorPrimary,
                }}
                type="primary"
                onClick={() => onAccessHandler()}
            // disabled={!(isbuttonDisable.isTextValid && isbuttonDisable.isVerifyTextValid)}
            >
                Add Access to this project
            </Button>,
            // </Popconfirm>,

        ],
    };
    return (
        <Modal
            open={details !== null}
            title={(
                <p className="font-semibold flex items-center gap-1.5">
                    <FaArrowRightArrowLeft className="" />
                    Add a Collaborator to
                    {' '}
                    {details?.name}
                </p>
            )}
            okText="Delete"
            okButtonProps={{ style: { display: "none" } }}
            width={430}
            {...footer}
            className="form-modal-title"
            onCancel={() => {
                setDetails(null);
                form.resetFields();
                // setIsbuttonDisable({
                //     isTextValid: false,
                //     isVerifyTextValid: false,
                // });
            }}
        >

            <div className="mb-3 mt-5">
                <p className="font-medium mb-2">
                    {details?.showText}

                </p>
                {/* <p className="bg-[#ff743d] text-sm rounded-[8px] px-2.5 py-1.5 text-[#fff]">
                    <span className="font-bold">Warning:</span>
                    This action is not reversible. Are you sure you want to proceed?
                </p> */}
            </div>
            <Form form={form}>

                <Row className="-mt-2.5">
                    <Col span={24}>
                        <p className="font-medium">
                            Please enter the Collaborator email
                        </p>
                    </Col>
                </Row>
                <Row gutter={[12, 12]}>
                    <Col span={16}>
                        <Form.Item
                            name="email"
                            rules={[
                                {
                                    required: true,
                                    message: "",
                                },
                                {
                                    type: 'email',
                                    message: "",
                                },
                                // {
                                //     validator: (_, value) => {
                                //         if (!value || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                                //             setIsbuttonDisable((prev) => {
                                //                 return {
                                //                     ...prev,
                                //                     isVerifyTextValid: true,
                                //                 };
                                //             });
                                //             setNewEmail(value);
                                //             return Promise.resolve();
                                //         }
                                //         setIsbuttonDisable((prev) => {
                                //             return {
                                //                 ...prev,
                                //                 isVerifyTextValid: false,
                                //             };
                                //         });
                                //         return Promise.reject('Invalid Email');
                                //     },
                                // },
                            ]}
                        >
                            <Input placeholder="Enter Email address" />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item
                            name="type"
                            rules={[
                                {
                                    required: true,
                                    message: "",
                                },
                            ]}
                        >
                            <Select
                                defaultValue="full-access"
                                // style={{ width: 120 }}
                                // onChange={handleChange}
                                options={accessOptions}
                            />
                        </Form.Item>
                    </Col>
                </Row>
            </Form>
        </Modal>
    );
};

export default CollaboratorModal;
