import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Spin } from "antd";
// import domainCreationImage1 from "../asset/image/jirabg.png";
import domainCreationImage1 from "../asset/image/domain1.gif";
import domainCreationImage2 from "../asset/image/domain2.gif";
import domainCreationImage3 from "../asset/image/domain3.gif";

// import domainCreationImage2 from "../asset/image/no image.png";
// import domainCreationImage3 from "../asset/image/jirabg.png";
// import { LoadingOutlined } from "@ant-design/icons";
const LoadingModal = ({ visible, submitData }) => {
  const [loadingText, setLoadingText] = useState("Creating Domain...");
  const [currentImage, setCurrentImage] = useState(domainCreationImage1);
  const texts = !submitData?.subDomain
    ? ["Almost there...", "Just a moment...", "Patience, please..."]
    : [
        `Verifying subdomain for ${submitData?.subDomain}... Just a moment.`,
        `Subdomain for ${submitData?.subDomain} almost ready. Just hold on.`,
        `Patience, please. Validating ${submitData?.subDomain}'s subdomain...`,
        `Building subdomain for ${submitData?.subDomain}. Just a few more moments.`,
        `Subdomain for ${submitData?.subDomain} in the works. Almost there.`,
        `Fine-tuning ${submitData?.subDomain}'s subdomain. Short wait.`,
        `Customizing subdomain for ${submitData?.subDomain}... Hang tight.`,
        `Subdomain for ${submitData?.subDomain} coming soon. Stay tuned.`,
      ];

  useEffect(() => {
    const images = [
      domainCreationImage1,
      domainCreationImage2,
      domainCreationImage3,
    ];
    const interval = setInterval(() => {
      // Change loading text randomly
      //   const texts = [
      //     "Almost there...",
      //     "Just a moment...",
      //     "Patience, please...",
      //   ];
      setLoadingText(texts[Math.floor(Math.random() * texts.length)]);
      setCurrentImage(images[Math.floor(Math.random() * images.length)]);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    // Change image smoothly every 10 seconds
    // const images = [
    //   domainCreationImage1,
    //   domainCreationImage2,
    //   domainCreationImage3,
    // ];
    const interval = setInterval(() => {
      // Fade out the image
      //   document.getElementById("loading-image").style.opacity = 0;

      setTimeout(() => {
        // Change the image
        // setCurrentImage(images[Math.floor(Math.random() * images.length)]);
        // Fade in the image
        // document.getElementById("loading-image").style.opacity = 1;
      }, 500); // Wait for the fade out transition before changing the image
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <Modal
      visible={visible}
      footer={null}
      mask={false}
      closable={false}
      className="flex items-center justify-center"
      //   width={1500}
      bodyStyle={{
        width: "650px",
        height: "450px",
      }}
    >
      <div className=" p-8 rounded-lg  flex flex-col items-center">
        <img
          id="loading-image"
          src={currentImage}
          alt="Domain Creation"
          className="loading-image w-full max-h-72 object-contain mb-4 rounded-lg transition-opacity duration-[2000s]"
        />
        <Spin size="large" className="mb-4" />
        {/* <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} /> */}
        <p className="text-lg text-[#ff743d] transition-opacity duration-500">
          {loadingText}
        </p>
        <p className="text-sm mt-4 text-gray-600 transition-opacity duration-500">
          Note: This process will take an average of 20 seconds and do not back
          out during the process
        </p>
      </div>
    </Modal>
  );
};

export default LoadingModal;
