/* eslint-disable no-unsafe-optional-chaining */
import {
  <PERSON><PERSON>, Card, Empty, Row, Spin, Typography,
} from "antd";
import React, { useEffect, useState } from "react";
import { Link, useParams } from "react-router-dom";
import moment from "moment";
import { BsArrowRight } from "react-icons/bs";
import useHttp from "../../../../../hooks/use-http";
import {
  apiGenerator,
  formatAmount,
  getMethodBadgeColor,
  getServiceRoute,
  isLogin,
} from "../../../../../util/functions";
import CONSTANTS from "../../../../../util/constant/CONSTANTS";
import Progress from "../../../../../component/chart/progress";

const { Text } = Typography;

const RequestDetailsCount = ({ dateRange = null, value = {} }) => {
  const { projectID, serviceID, environmentID } = useParams();
  const API = useHttp();
  const [requestDetails, setRequestDetails] = useState([]);

  useEffect(() => {
    if (!isLogin()) return;
    // Set endDateString to the current date
    const toDay = moment().utc();
    const toDayString = toDay.format("YYYY-MM-DD");
    let extraPerms = `&startDate=${toDayString}&endDate=${toDayString}&limit=9`;

    if (dateRange?.startDate && dateRange?.endDate) {
      extraPerms = `&startDate=${dateRange?.startDate}&endDate=${dateRange?.endDate}&limit=9`;
    }
    if (value?.extraPerms) {
      extraPerms += `&${value?.extraPerms}`;
    }

    API.sendRequest(
      apiGenerator(
        CONSTANTS.API.utilization.requestDetails,
        {
          serviceID: environmentID,
        },
        extraPerms,
      ),
      (res) => {
        setRequestDetails(res?.data);
      },
    );
  }, [serviceID, environmentID, dateRange]);

  const totalCount = requestDetails.reduce(
    (total, currant) => total + +currant?.totalCount,
    0,
  );

  return (
    <Card className="h-full overflow-y-scroll">
      <Row className="justify-between items-center mb-5">
        <Text>End-Points Utilization</Text>
      </Row>

      {requestDetails?.length !== 0 && !API.isLoading && (
        <div className="flex mb-3 flex-col gap-4">
          {requestDetails?.map((process) => (
            <div key={process?.originalUrl}>
              <Row className="justify-between items-center">
                <Text
                  className="!m-0 !p-0 !ms-0 "
                  style={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    maxWidth: "90%",
                  }}
                >
                  <Badge
                    count={process?.method}
                    size="small"
                    color={getMethodBadgeColor(process?.method)}
                  />
                  {" "}
                  {process?.endPoint}
                </Text>
                <Text type="secondary">
                  {formatAmount(process?.totalCount || 0)}
                </Text>
              </Row>
              <Progress
                height="5px"
                progress={[
                  {
                    color: "#ff743d",
                    progress: `${+(
                      (+process?.totalCount * 100)
                      / +totalCount
                    )?.toFixed(0)}%`,
                  },
                ]}
              />
            </div>
          ))}
        </div>
      )}

      {API.isLoading && (
        <div className=" h-96  w-full">
          <Spin className="mt-48" tip="Loading..." size="large">
            <div className="content" />
          </Spin>
        </div>
      )}
      {requestDetails?.length !== 0 && !API.isLoading && (
        <Link
          to={`${getServiceRoute({
            projectId: projectID,
            serviceID,
            environmentID,
          })}/endpoint-utilization`}
          className="flex items-center gap-1"
        >
          View More
          {' '}
          <BsArrowRight />
        </Link>
      )}

      {requestDetails?.length === 0 && !API.isLoading && (
        <div className="w-full h-96 flex justify-center items-center">
          <Empty />
        </div>
      )}
    </Card>
  );
};

export default RequestDetailsCount;
