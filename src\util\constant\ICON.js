/* eslint-disable react/react-in-jsx-scope */
import maintananceDashboardIcon from "../../asset/image/icons/maintananceDashboard.svg";
import AttendanceDashboardIcon from "../../asset/image/icons/AttendanceDashboard.svg";
import InventoryDashboardIcon from "../../asset/image/icons/InventoryDashboard.svg";
import ExpensesDashboardIcon from "../../asset/image/icons/ExpensesDashboard.svg";
import CleaningLogDashboardIcon from "../../asset/image/icons/CleaningLogDashboard.svg";
import ChecklistDashboardIcon from "../../asset/image/icons/ChecklistDashboard.svg";
import RefreshDashboardIcon from "../../asset/image/icons/RefreshDashboard.svg";

export {
  maintananceDashboardIcon,
  ChecklistDashboardIcon,
  CleaningLogDashboardIcon,
  ExpensesDashboardIcon,
  AttendanceDashboardIcon,
  InventoryDashboardIcon,
  RefreshDashboardIcon,
};

export const IpSettingIcon = (props) => (
  <svg
    width={15}
    height={14}
    viewBox="0 0 15 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="ip-setting-icon"
    {...props}
  >
    <g clipPath="url(#clip0_3045_2086)">
      <path
        d="M7.94446 14H7.05559C6.62675 14 6.2778 13.6511 6.2778 13.2222V12.5284C5.6261 12.3856 5.015 12.1328 4.45443 11.7741L3.96443 12.2639C3.66103 12.5673 3.16801 12.5673 2.8646 12.2639L2.23613 11.6354C1.93294 11.3322 1.93294 10.8388 2.23613 10.5356L2.72591 10.0456C2.36721 9.48504 2.11437 8.8739 1.97158 8.2222H1.27779C0.84895 8.2222 0.5 7.87325 0.5 7.44441V6.55554C0.5 6.12671 0.84895 5.77776 1.27779 5.77776H1.97158C2.11437 5.12606 2.36721 4.51496 2.72591 3.95439L2.23613 3.46439C1.93294 3.1612 1.93294 2.66774 2.23613 2.36456L2.8646 1.73609C3.16801 1.43268 3.66103 1.43268 3.96443 1.73609L4.45443 2.22587C5.01496 1.86716 5.6261 1.61433 6.2778 1.47153V0.777788C6.2778 0.34895 6.62675 0 7.05559 0H7.94446C8.37329 0 8.72224 0.34895 8.72224 0.777788V1.47158C9.37394 1.61437 9.98504 1.86721 10.5456 2.22591L11.0356 1.73613C11.339 1.43273 11.832 1.43273 12.1354 1.73613L12.7639 2.3646C13.0671 2.66779 13.0671 3.16124 12.7639 3.46443L12.2741 3.95443C12.6328 4.51496 12.8857 5.1261 13.0285 5.7778H13.7223C14.1511 5.7778 14.5 6.12675 14.5 6.55559V7.44446C14.5 7.87329 14.1511 8.22224 13.7223 8.22224H13.0285C12.8857 8.87394 12.6328 9.48504 12.2741 10.0456L12.7639 10.5356C13.0671 10.8388 13.0671 11.3323 12.7639 11.6354L12.1354 12.2639C11.832 12.5673 11.339 12.5673 11.0356 12.2639L10.5456 11.7741C9.98509 12.1328 9.37394 12.3857 8.72224 12.5285V13.2223C8.72224 13.6511 8.37329 14 7.94446 14ZM4.41322 11.0106C4.48073 11.0106 4.54819 11.031 4.60659 11.0725C5.22356 11.5118 5.91669 11.7986 6.66647 11.9247C6.82708 11.9518 6.94446 12.0907 6.94446 12.2535V13.2223C6.94446 13.2835 6.99438 13.3334 7.05559 13.3334H7.94446C8.00566 13.3334 8.05558 13.2835 8.05558 13.2223V12.2535C8.05558 12.0907 8.17301 11.9518 8.33357 11.9247C9.08336 11.7986 9.77649 11.5118 10.3934 11.0725C10.5263 10.9785 10.7075 10.9929 10.8225 11.1083L11.507 11.7925C11.5504 11.8359 11.6207 11.8359 11.6641 11.7925L12.2925 11.1641C12.3357 11.1207 12.3357 11.0504 12.2925 11.007L11.6083 10.3225C11.4933 10.2075 11.4781 10.0261 11.5725 9.89345C12.0118 9.27649 12.2986 8.58336 12.4247 7.83357C12.4518 7.67296 12.5907 7.55558 12.7535 7.55558H13.7223C13.7835 7.55558 13.8334 7.50566 13.8334 7.44446V6.55559C13.8334 6.49438 13.7835 6.44446 13.7223 6.44446H12.7535C12.5907 6.44446 12.4518 6.32704 12.4247 6.16647C12.2986 5.41669 12.0118 4.72356 11.5725 4.10659C11.4781 3.97399 11.4933 3.79256 11.6083 3.67754L12.2925 2.99307C12.3357 2.94967 12.3357 2.87936 12.2925 2.83596L11.6641 2.20749C11.6207 2.16409 11.5504 2.16409 11.507 2.20749L10.8225 2.89174C10.7075 3.0072 10.5263 3.02151 10.3934 2.92753C9.77649 2.48828 9.08336 2.20141 8.33357 2.07533C8.17296 2.0482 8.05558 1.90929 8.05558 1.74654V0.777788C8.05558 0.716581 8.00566 0.666663 7.94446 0.666663H7.05559C6.99438 0.666663 6.94446 0.716581 6.94446 0.777788V1.74654C6.94446 1.90929 6.82704 2.0482 6.66647 2.07533C5.91669 2.20141 5.22356 2.48828 4.60659 2.92753C4.47399 3.02151 4.29277 3.00716 4.17754 2.89174L3.49307 2.20749C3.44967 2.16409 3.37936 2.16409 3.33596 2.20749L2.70749 2.83596C2.66431 2.87936 2.66431 2.94967 2.70749 2.99307L3.39174 3.67754C3.50676 3.79256 3.52194 3.97399 3.42753 4.10659C2.98828 4.72356 2.70141 5.41669 2.57533 6.16647C2.5482 6.32708 2.40929 6.44446 2.24654 6.44446H1.27779C1.21658 6.44446 1.16666 6.49438 1.16666 6.55559V7.44446C1.16666 7.50566 1.21658 7.55558 1.27779 7.55558H2.24654C2.40929 7.55558 2.5482 7.67301 2.57533 7.83357C2.70141 8.58336 2.98828 9.27649 3.42753 9.89345C3.52194 10.0261 3.50676 10.2075 3.39174 10.3225L2.70749 11.007C2.66431 11.0504 2.66431 11.1207 2.70749 11.1641L3.33596 11.7925C3.37936 11.8359 3.44967 11.8359 3.49307 11.7925L4.17754 11.1083C4.24198 11.0439 4.32751 11.0106 4.41322 11.0106Z"
        fill="#0F172A"
      />
      <path
        d="M7.49998 10.8891C5.35566 10.8891 3.61108 9.14454 3.61108 7.00022C3.61108 4.8559 5.35566 3.11133 7.49998 3.11133C9.6443 3.11133 11.3889 4.8559 11.3889 7.00022C11.3889 9.14454 9.6443 10.8891 7.49998 10.8891ZM7.49998 3.77799C5.72329 3.77799 4.27775 5.22353 4.27775 7.00022C4.27775 8.77691 5.72329 10.2225 7.49998 10.2225C9.27667 10.2225 10.7222 8.77691 10.7222 7.00022C10.7222 5.22353 9.27667 3.77799 7.49998 3.77799Z"
        fill="#0F172A"
      />
      <path
        d="M7.49991 10.8891C6.40572 10.8891 5.83325 8.93275 5.83325 7.00022C5.83325 5.0677 6.40572 3.11133 7.49991 3.11133C8.5941 3.11133 9.16656 5.0677 9.16656 7.00022C9.16656 8.93275 8.5941 10.8891 7.49991 10.8891ZM7.49991 3.77799C7.10191 3.77799 6.49991 5.06315 6.49991 7.00022C6.49991 8.9373 7.10191 10.2225 7.49991 10.2225C7.8979 10.2225 8.4999 8.9373 8.4999 7.00022C8.4999 5.06315 7.8979 3.77799 7.49991 3.77799Z"
        fill="#0F172A"
      />
      <path
        d="M10.6112 7.33317H4.389C4.20498 7.33317 4.05566 7.18385 4.05566 6.99984C4.05566 6.81582 4.20498 6.6665 4.389 6.6665H10.6112C10.7952 6.6665 10.9445 6.81582 10.9445 6.99984C10.9445 7.18385 10.7952 7.33317 10.6112 7.33317Z"
        fill="#0F172A"
      />
    </g>
    <defs>
      <clipPath id="clip0_3045_2086">
        <rect width={14} height={14} fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);

export const BlockedIpIcon = (props) => (
  <svg
    width={15}
    height={14}
    viewBox="0 0 15 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="blocked-ip-icon"
    {...props}
  >
    <path
      d="M10.4641 7.63086C10.7894 7.631 11.0319 7.78165 11.1917 7.96094C11.2713 8.05038 11.3305 8.14766 11.3704 8.2373C11.4098 8.3262 11.4319 8.4119 11.4319 8.47754V9.0498C11.6747 9.10933 11.8459 9.31367 11.8459 9.55566V10.96C11.8457 11.25 11.5993 11.4824 11.301 11.4824H9.58423C9.28584 11.4824 9.03955 11.2501 9.03931 10.96V9.55566C9.03931 9.31199 9.21329 9.10654 9.45435 9.0498V8.47754C9.45435 8.22724 9.5824 8.01381 9.77075 7.86523C9.95873 7.71705 10.21 7.63086 10.4641 7.63086ZM9.56567 9.5332L9.56665 9.53418C9.55876 9.53556 9.55082 9.54028 9.54517 9.5459C9.5394 9.55164 9.53931 9.5556 9.53931 9.55566V10.96C9.53957 10.9613 9.54042 10.9656 9.54712 10.9707C9.55533 10.9769 9.5688 10.9814 9.58423 10.9814H11.301C11.3164 10.9814 11.3299 10.9769 11.3381 10.9707C11.3448 10.9656 11.3457 10.9613 11.3459 10.96V9.55469L11.345 9.55273C11.3442 9.55115 11.3428 9.54847 11.3401 9.5459L11.3118 9.5332C11.3109 9.53312 11.3099 9.53329 11.3088 9.5332C11.306 9.53297 11.3034 9.53224 11.301 9.53223H11.1917L11.1809 9.53516L11.1711 9.53223H9.71509L9.70435 9.53516L9.69458 9.53223H9.58423C9.57944 9.53223 9.57413 9.53203 9.56567 9.5332ZM10.4641 8.09766C10.2968 8.09766 10.1681 8.14187 10.0833 8.20996C9.99926 8.27743 9.95435 8.37114 9.95435 8.47754V9.03223H10.9319V8.47754C10.9319 8.45795 10.9239 8.42297 10.9045 8.38086C10.8856 8.33978 10.8566 8.29412 10.8176 8.25195C10.7399 8.16796 10.6233 8.09779 10.4641 8.09766Z"
      fill="#0F172A"
      stroke="#0F172A"
      strokeWidth={0.1}
      {...props}
    />
    <path
      d="M6.74023 1.20459C9.65694 1.20478 12.0293 3.57768 12.0293 6.49463C12.0293 6.65044 12.0195 6.8104 12.0049 6.97119C12.9276 7.51599 13.5497 8.51807 13.5498 9.66553C13.5498 11.3914 12.1458 12.7954 10.4199 12.7954C9.43837 12.7953 8.56324 12.3394 7.98926 11.6304C7.58122 11.7299 7.16245 11.7846 6.74023 11.7847C4.55024 11.7847 2.56317 10.4106 1.79199 8.36475V8.36572C1.79187 8.36544 1.79145 8.36393 1.79102 8.36279V8.36182L1.78809 8.35596C1.56371 7.76997 1.45021 7.14338 1.4502 6.49463C1.4502 5.84574 1.56423 5.21862 1.78906 4.63135V4.63037C1.78986 4.62838 1.79083 4.62591 1.79102 4.62549C1.79166 4.62361 1.79317 4.62019 1.79492 4.6167C2.56724 2.57552 4.5524 1.20439 6.74023 1.20459ZM10.4199 7.03564C8.97029 7.03577 7.79015 8.21568 7.79004 9.66553C7.79004 11.1155 8.97002 12.2953 10.4199 12.2954C11.8699 12.2954 13.0498 11.1155 13.0498 9.66553C13.0497 8.21561 11.8697 7.03564 10.4199 7.03564ZM4.91309 8.51807C5.08142 9.34906 5.34418 10.0375 5.66113 10.52C5.98679 11.0157 6.36181 11.2847 6.74023 11.2847C7.00794 11.2846 7.27413 11.1491 7.53418 10.8735C7.37803 10.5016 7.29004 10.0937 7.29004 9.66553C7.29007 9.26062 7.36905 8.87368 7.50977 8.51807H4.91309ZM2.40234 8.51807C3.00148 9.80139 4.14336 10.746 5.4873 11.1138C5.00133 10.5459 4.6198 9.64414 4.4043 8.51807H2.40234ZM4.82617 4.96924C4.75335 5.45594 4.71387 5.96755 4.71387 6.49463C4.71387 7.02177 4.75352 7.5323 4.82617 8.01807H7.76465C8.01412 7.61717 8.35063 7.27697 8.74902 7.02393C8.75779 6.84824 8.76562 6.67161 8.76562 6.49463C8.76562 5.96562 8.72595 5.4546 8.65332 4.96924H4.82617ZM2.20312 4.96924C2.03769 5.45404 1.9502 5.96558 1.9502 6.49463C1.95021 7.02376 2.03818 7.53433 2.20312 8.01807H4.32031C4.25127 7.53041 4.21387 7.02078 4.21387 6.49463C4.21387 5.96854 4.25093 5.45766 4.32031 4.96924H2.20312ZM9.15918 4.96924C9.22837 5.45659 9.26562 5.96762 9.26562 6.49463C9.26562 6.58385 9.2603 6.67344 9.25684 6.76123C9.61663 6.61647 10.0088 6.53567 10.4199 6.53564C10.8075 6.53564 11.1779 6.61023 11.5205 6.73975C11.525 6.65687 11.5303 6.57506 11.5303 6.49463C11.5303 5.96125 11.4401 5.44871 11.2783 4.96924H9.15918ZM5.48633 1.87451C4.14293 2.24225 3.00192 3.18726 2.40332 4.46924H4.4043C4.61974 3.34411 5.00047 2.44227 5.48633 1.87451ZM7.99219 1.87354C8.47833 2.441 8.85948 3.34264 9.0752 4.46826H11.0771C10.4852 3.20607 9.3597 2.2446 7.99219 1.87354ZM6.74023 1.70459C6.3619 1.70459 5.98671 1.97289 5.66113 2.46826C5.34436 2.95037 5.08141 3.63816 4.91309 4.46826H8.56641C8.39807 3.63804 8.13515 2.95034 7.81836 2.46826C7.49288 1.97307 7.11845 1.70475 6.74023 1.70459Z"
      fill="#0F172A"
      stroke="#0F172A"
      strokeWidth={0.1}
      {...props}
    />
  </svg>
);
