import { <PERSON><PERSON>, <PERSON>, Col, Row, Spin, notification, Statistic, Alert } from 'antd';
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { SettingOutlined, ShieldOutlined, ClockCircleOutlined, WarningOutlined } from '@ant-design/icons';
import ModalFormCreator from '../../../../component/common/ModalFormCreator';
import { rateLimitDummyAPI } from '../../../../util/dummyData/rateLimitData';

const IPSettingPage = () => {
    const [rateLimitConfig, setRateLimitConfig] = useState(null);
    const [loading, setLoading] = useState(true);
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [updating, setUpdating] = useState(false);
    const { environmentID } = useParams();

    useEffect(() => {
        fetchRateLimitConfig();
    }, [environmentID]);

    const fetchRateLimitConfig = async () => {
        try {
            setLoading(true);
            const response = await rateLimitDummyAPI.getRateLimitConfig(environmentID);
            if (response.success) {
                setRateLimitConfig(response.data);
            }
        } catch (error) {
            notification.error({
                message: 'Error',
                description: 'Failed to fetch rate limit configuration',
            });
        } finally {
            setLoading(false);
        }
    };

    const handleUpdateConfig = async (values) => {
        try {
            setUpdating(true);
            const response = await rateLimitDummyAPI.updateRateLimitConfig(environmentID, values);
            if (response.success) {
                setRateLimitConfig(response.data);
                setEditModalOpen(false);
                notification.success({
                    message: 'Success',
                    description: 'Rate limit configuration updated successfully',
                });
            }
        } catch (error) {
            notification.error({
                message: 'Error',
                description: 'Failed to update rate limit configuration',
            });
        } finally {
            setUpdating(false);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <Spin size="large" tip="Loading rate limit configuration..." />
            </div>
        );
    }

    const detials = [
        {
            id: 1,
            label: "Rate Limit (per minute)",
            value: `${rateLimitConfig?.rateLimit || 0} requests`,
            icon: <ShieldOutlined className="text-blue-500" />,
        },
        {
            id: 2,
            label: "Time Window",
            value: `${rateLimitConfig?.timeWindow || 0} minute(s)`,
            icon: <ClockCircleOutlined className="text-green-500" />,
        },
        {
            id: 3,
            label: "Block Duration",
            value: `${rateLimitConfig?.blockDuration || 0} minute(s)`,
            icon: <WarningOutlined className="text-orange-500" />,
        },
        {
            id: 4,
            label: "Permanent Block Threshold",
            value: `${rateLimitConfig?.permanentBlockThreshold || 0} violations`,
            icon: <WarningOutlined className="text-red-500" />,
        },
        {
            id: 5,
            label: "Rate Limit Message",
            value: (
                <p className="overflow-hidden break-all text-gray-600">
                    {rateLimitConfig?.rateLimitMessage || "No custom message set"}
                </p>
            ),
        },
        {
            id: 6,
            label: "IP Block Message",
            value: (
                <p className="overflow-hidden break-all text-gray-600">
                    {rateLimitConfig?.ipBlockedMessage || "No custom message set"}
                </p>
            ),
        },
    ];
    return (
        <>
            <ModalFormCreator
                loading={updating}
                open={editModalOpen}
                onCreate={handleUpdateConfig}
                onCancel={() => setEditModalOpen(false)}
                menu="RATE_LIMIT_MODAL"
                formData={rateLimitConfig}
                name="Edit Rate Limit Configuration"
                SubmitName="Update Configuration"
            />

            <Row gutter={[16, 24]}>
                <Col span={24}>
                    <Card
                        title={
                            <div className="flex items-center gap-2">
                                <ShieldOutlined className="text-blue-500" />
                                <span>Rate Limit Configuration</span>
                            </div>
                        }
                        extra={
                            <Button
                                type="primary"
                                icon={<SettingOutlined />}
                                onClick={() => setEditModalOpen(true)}
                                className="textcolor"
                            >
                                Configure Settings
                            </Button>
                        }
                    >
                        <Row gutter={[16, 16]}>
                            {detials?.map((ele) => (
                                <Col span={24} md={12} lg={8} key={ele?.id}>
                                    <Card size="small" className="h-full">
                                        <div className="flex items-start gap-3">
                                            {ele?.icon && (
                                                <div className="mt-1">
                                                    {ele?.icon}
                                                </div>
                                            )}
                                            <div className="flex-1">
                                                <div className="text-gray-500 text-sm mb-1">
                                                    {ele?.label}
                                                </div>
                                                <div className="font-medium text-gray-900">
                                                    {ele?.value}
                                                </div>
                                            </div>
                                        </div>
                                    </Card>
                                </Col>
                            ))}
                        </Row>

                        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                            <div className="flex items-start gap-3">
                                <ShieldOutlined className="text-blue-500 mt-1" />
                                <div>
                                    <h4 className="text-blue-900 font-medium mb-2">How Rate Limiting Works</h4>
                                    <ul className="text-blue-800 text-sm space-y-1">
                                        <li>• Requests are tracked per IP address within the specified time window</li>
                                        <li>• When limit is exceeded, the IP is temporarily blocked for the block duration</li>
                                        <li>• After reaching the permanent block threshold, the IP is permanently blocked</li>
                                        <li>• Custom messages are shown to blocked users</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </Card>
                </Col>
            </Row>
        </>
    );
};

export default IPSettingPage;
