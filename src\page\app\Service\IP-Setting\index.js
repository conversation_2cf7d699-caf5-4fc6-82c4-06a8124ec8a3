import { <PERSON><PERSON>, <PERSON>, <PERSON>, Row } from 'antd';
import React from 'react';

const IPSettingPage = () => {
     const detials = [
        {
          id: 1,
          label: "Rate Limit",
          value: "1000" || "-",
        },
        {
          id: 2,
          label: "Permanent Block",
          value: (
            <p className="overflow-hidden break-all">
              {"10" || "-"}
            </p>
          ),
        },
        {
          id: 3,
          label: "Duration",
          value: "15 min" || "-",
        },
        {
          id: 4,
          label: "Count",
          value: "100" || "-",
            // (serviceDetails?.createdAt
            //   && `${moment(serviceDetails?.createdAt)
            //     .utc()
            //     .format("MMM DD")} at ${moment
            //       .utc(serviceDetails?.createdAt)
            //       .local()
            //       .format("hh:mm:ss A")}`)
            // || "-",
        },
        {
          id: 5,
          label: "Rate Limit Excesses Message",
          value: "Rate limit exceeded" || "-",
        },
         {
          id: 5,
          label: "Block IP Message",
          value: "Rate limit exceeded" || "-",
        },
      ];
    return (
       <>
       <Row gutter={[16, 24]}>
        <Col span={24} lg={24} xl={24} xxl={24}>
                <Card title="General IP Setting">
                  {detials?.map((ele) => (
                    <Row
                      gutter={[16, 16]}
                      className="mb-1.5 font-medium "
                      key={ele?.id}
                    >
                      <Col span={5}>
                        {ele?.label}
                        &nbsp;:
                      </Col>
                      <Col span={19}>{ele?.value}</Col>
                    </Row>
                  ))}
                  <Button
                    className="my-4 px-4 textcolor"
                    type="primary"
                    onClick={() => {
                      setEditServiceDetails(serviceDetails);
                    }}
                  >
                    Change Service Details
                  </Button>
                </Card>
              </Col>
              
              </Row>
       </>
    );
};

export default IPSettingPage;
