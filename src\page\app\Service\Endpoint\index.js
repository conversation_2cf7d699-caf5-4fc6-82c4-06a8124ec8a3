/* eslint-disable react/jsx-boolean-value */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-props-no-multi-spaces */
/* eslint-disable no-unsafe-optional-chaining */
import React, { useEffect, useRef, useState } from "react";
import {
  Button, Card, Col, Pagination, Popconfirm, Row, Switch, Table, Typography,
} from "antd";
import { useNavigate, useParams } from "react-router-dom";
import Search from "antd/es/input/Search";
import { FaDownload } from "react-icons/fa";
import { CSVLink } from "react-csv";
import CONSTANTS, { projectRoot, serviceRoot } from "../../../../util/constant/CONSTANTS";
import useHttp from "../../../../hooks/use-http";
import { convertLocalStringWithoutRupee, formatAmount, getMaxTimeUnit } from "../../../../util/functions";
import CustomButton from "../../../../component/common/Custom-Buttons";

const { Text } = Typography;
let totalEndpointCounts = 0;

const EndpointUtilization = ({
  mode = null,
  dateQuery = null,
  setReload = null,
  isVisible = false,
}) => {
  const [apiOverView, setApiOverView] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [extraQuery, setExtraQuery] = useState("");
  const [methodQuery, setMethodQuery] = useState("");
  const [typeQuery, setTypeQuery] = useState("");
  const [refresh, setRefresh] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: 10,
    total: 0,
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [switchValues, setSwitchValues] = useState({});
  const [searchValue, setSearchValue] = useState("");
  const [allData, setAllData] = useState([]);
  const excelRef = useRef();
  // optimize typeQuery in the one state
  const debounceTimeout = useRef(null);

  const { projectID, serviceID, environmentID } = useParams();
  const api = useHttp();
  const navigate = useNavigate();

  const headers = [
    {
      title: "No.",
      dataIndex: "no",
      key: "no",
      width: 100,
    },
    {
      title: "Endpoint",
      dataIndex: "endPoint",
      key: "endPoint",
      width: 150,
    },
    {
      title: "Type",
      dataIndex: "type",
      key: "type",
      width: 120,
      filters: mode ? null : [
        {
          text: "Internal",
          value: "Internal",
        },
        {
          text: "ThirdParty",
          value: "ThirdParty",
        },
      ],
      filterMode: "tree",
      filterSearch: !mode,
    },
    {
      title: "Method",
      dataIndex: "method",
      className: "!text-center",
      key: "method",
      width: 90,
      filters: [
        {
          text: "GET",
          value: "GET",
        },
        {
          text: "POST",
          value: "POST",
        },
        {
          text: "PATCH",
          value: "PATCH",
        },
        {
          text: "PUT",
          value: "PUT",
        },
        {
          text: "DELETE",
          value: "DELETE",
        },
        {
          text: "HEAD",
          value: "HEAD",
        },
        {
          text: "OPTIONS",
          value: "OPTIONS",
        },
      ],
      filterMode: "tree",
      filterSearch: true,
    },

    {
      title: "Total Count",
      dataIndex: "totalCount",
      className: "!text-center",
      key: "totalCount",
      width: 80,
      sorter: true,
    },
    {
      title: "Success Count",
      dataIndex: "successCount",
      className: "!text-center",
      key: "successCount",
      width: 90,
      sorter: true,
    },
    {
      title: "Fail Count",
      dataIndex: "failCount",
      className: "!text-center",
      key: "failCount",
      width: 90,
      sorter: true,
    },
    {
      title: "Average Processing Time",
      dataIndex: "avgResponseTime",
      className: "!text-center",
      key: "avgResponseTime",
      width: 110,
      sorter: true,
    },
    {
      title: "Visible",
      dataIndex: "isVisible",
      className: "!text-center",
      key: "isVisible",
      width: 80,
      sorter: true,
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (value) => {
      console.log(value);
      setSelectedRowKeys(value);
    },
  };
  const updataeVisibility = (payload) => {
    const VISIBILITY_API = { ...CONSTANTS.API.overview.updateApiVisibility };
    api.sendRequest(
      VISIBILITY_API,
      () => {
        setRefresh((prev) => !prev);
        setReload((prev) => !prev);
      },
      payload,
      "Endpoint Blocked Successfully !!!",
    );
  };

  const blukVisibilityHandler = () => {
    const payload = {
      serviceEnvironmentId: environmentID,
      isVisible: false,
      data: selectedRowKeys?.map((ele) => {
        return {
          method: ele?.method,
          endPoint: ele?.endPoint,
        };
      }),
    };
    const BULK_VISIBILITY_API = { ...CONSTANTS.API.overview.updateBulkApiVisibility };
    api.sendRequest(
      BULK_VISIBILITY_API,
      () => {
        setRefresh((prev) => !prev);
      },
      payload,
      "Endpoints Blocked Successfully !!!",
    );
  };

  const blukAllVisibilityHandler = () => {
    const payload = {
      serviceEnvironmentId: environmentID,
      isVisible: false,
      endPointPattern: filterText ?? "",
    };
    if (filterText !== "" && filterText !== undefined && filterText !== null) {
      payload.endPointPattern = filterText;
    }
    const BULK_VISIBILITY_API = { ...CONSTANTS.API.overview.updateAllBulkApiVisibility };
    api.sendRequest(
      BULK_VISIBILITY_API,
      () => {
        setRefresh((prev) => !prev);
        // setfilterText("");
        setFilterText("");
        setSearchValue("");
        setSelectedRowKeys([]);
      },
      payload,
      "All Endpoints Blocked Successfully !!!",
    );
  };

  const handleTableChange = (_e, filters, sorter) => {
    if (filters?.method) {
      setMethodQuery(`&method=${filters?.method?.toString()}`);
    } else {
      setMethodQuery("");
    }

    if (filters?.type) {
      setTypeQuery(`&type=${filters?.type?.toString()}`);
    } else {
      setTypeQuery("");
    }

    if (sorter?.order) {
      setExtraQuery(
        `&sort=${sorter?.columnKey}&sortBy=${sorter?.order === "descend" ? "DESC" : "ASC"
        }`,
      );
    } else {
      setExtraQuery("");
    }
  };
  const searchHandler = (value) => {
    // Clear the previous timeout if it exists
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
    }

    // Set a new timeout to call the debounced function after a certain delay
    debounceTimeout.current = setTimeout(() => {
      setPagination((prev) => ({ ...prev, currentPage: 1 }));
      setFilterText(value);
    }, 500); // Adjust the delay as needed
  };

  useEffect(() => {
    const OVERVIEW_API = { ...CONSTANTS.API.overview.getApiCountOverView };
    OVERVIEW_API.endpoint = OVERVIEW_API.endpoint.replace(
      ":serviceID",
      environmentID,
    );
    OVERVIEW_API.endpoint = `${OVERVIEW_API.endpoint}&isVisible=${isVisible ? 1 : 0
      }&page=${pagination.currentPage}&limit=${pagination.pageSize}`;

    if (dateQuery.startDate !== null && dateQuery.endDate !== null) {
      const query = `&startDate=${dateQuery.startDate}&endDate=${dateQuery.endDate}`;
      OVERVIEW_API.endpoint += query;
    }
    if (mode) {
      const query = `&type=${mode}`;
      OVERVIEW_API.endpoint += query;
    }
    if (filterText != null && filterText !== "") {
      const query = `&search=${filterText}`;
      OVERVIEW_API.endpoint += query;
    }
    OVERVIEW_API.endpoint = OVERVIEW_API.endpoint + extraQuery + methodQuery + typeQuery;
    api.sendRequest(OVERVIEW_API, (res) => {
      setSelectedRowKeys([]);
      setPagination((prevPagination) => ({
        ...prevPagination,
        total: res?.data?.count ?? 90000,
      }));
      totalEndpointCounts = res?.data?.count;
      setApiOverView(
        res?.data?.rows?.map((ele, i) => {
          const latency = getMaxTimeUnit(ele?.avgResponseTime);
          return {
            ...ele,
            key: ele,
            no:
              pagination.currentPage === 1
                ? i + 1
                : pagination.pageSize * (pagination.currentPage - 1) + i + 1,
            totalCount: formatAmount(ele?.totalCount) || "0",
            successCount: (
              <Text className="text-green-700">
                {formatAmount(ele?.successCount) || "0"}
                {' '}
                (
                {ele?.successCount && ele?.totalCount !== 0
                  ? ((ele?.successCount / ele?.totalCount) * 100)?.toFixed(0)
                  : "0"}
                %)
              </Text>
            ),
            failCount: (
              <Text className="text-red-700">
                {formatAmount(ele?.failCount) || "0"}
                {' '}
                (
                {ele?.failCount && ele?.totalCount !== 0
                  ? ((ele?.failCount / ele?.totalCount) * 100)?.toFixed(0)
                  : "0"}
                %)
                {" "}
              </Text>
            ),
            avgResponseTime: `${latency.time} ${latency.unit}`,
            method: ele?.method || "-",
            endPointD: ele?.endPoint,
            endPoint:
              <p className="overflow-hidden break-all">{ele?.endPoint}</p>
              || "-",
            isVisible: (
              <div
                onKeyDown={(e) => {
                  if (e.key === 'Escape') {
                    setSwitchValues((prev) => ({
                      ...prev,
                      [ele?.endPoint]: ele?.isVisible,
                    }));
                  }
                }}
              >
                <Popconfirm
                  key={ele?.endPoint + ele?.menthod}
                  title="Are you sure you want to Block the API data?"
                  description={(
                    <p>
                      Blocking the API data will make it invisible on the map. However, you will be able to
                      <br />
                      unblock it later from the Blocked API section if needed.
                    </p>
                  )}
                  onConfirm={() => {
                    const value = !ele?.isVisible;
                    const payload = {
                      endPoint: ele?.endPoint,
                      method: ele?.method,
                      serviceEnvironmentId: environmentID,
                      isVisible: value,
                    };
                    updataeVisibility(payload);
                  }}
                  onCancel={() => {
                    setSwitchValues((prev) => ({
                      ...prev,
                      [ele?.endPoint]: ele?.isVisible,
                    }));
                  }}
                >
                  <Switch
                    key={ele?.endPoint + ele?.menthod}
                    checked={switchValues[ele?.endPoint] ?? ele?.isVisible}
                    onChange={(checked) => {
                      setSwitchValues((prev) => ({
                        ...prev,
                        [ele?.endPoint]: checked,
                      }));
                    }}
                  />
                </Popconfirm>
              </div>
            ),
          };
        }),
      );
    });
  }, [
    environmentID,
    filterText,
    extraQuery,
    methodQuery,
    mode,
    dateQuery,
    typeQuery,
    refresh,
    pagination.currentPage,
    pagination.pageSize,
    switchValues,
  ]);

  useEffect(() => {
    if (allData?.length <= 0) return;
    allData?.length > 0 && excelRef.current.link.click();
    setAllData([]);
  }, [allData]);

  const exportData = () => {
    const OVERVIEW_API = { ...CONSTANTS.API.overview.getApiCountOverView };
    OVERVIEW_API.endpoint = OVERVIEW_API.endpoint.replace(
      ":serviceID",
      environmentID,
    );
    OVERVIEW_API.endpoint = `${OVERVIEW_API.endpoint}&isVisible=${isVisible ? 1 : 0
      }`;
    if (mode) {
      const query = `&type=${mode}`;
      OVERVIEW_API.endpoint += query;
    }
    if (filterText != null && filterText !== "") {
      const query = `&search=${filterText}`;
      OVERVIEW_API.endpoint += query;
    }
    api.sendRequest(OVERVIEW_API, (res) => {
      const data = res?.data?.rows?.map((ele) => {
        return {
          endPoint: ele?.endPoint,
          type: ele?.type,
          method: ele?.method,
          totalCount: ele?.totalCount,
          successCount: ele?.successCount,
          failCount: ele?.failCount,
          avgResponseTime: ele?.avgResponseTime,
          // isVisible: ele?.isVisible ? "Yes" : "No",
        };
      });
      setAllData(data);
      // setTimeout(() => {
      //   // done(false);
      //   excelRef.current.link.click();
      // }, 1000);
    });
  };

  return (
    <>
      <Card>
        <div className="flex items-center justify-between gap-3">
          <Text className="hidden  md:block">Endpoint Utilization</Text>
          <div className="flex gap-3 items-center">
            {mode !== "ThirdParty" && (
              <Button type="primary" onClick={() => navigate(`${projectRoot}/${serviceRoot}/${projectID}/${serviceID}/${environmentID}/api/apiallow`)}>
                Whitelisted Pattern
              </Button>
            )}
            {apiOverView?.length > 0 && (
              <Button
                className="btn-dashboard-icon textcolor"
                type="primary"
                // ghost
                icon={<FaDownload size={13} />}
                // size="large"
                style={{ margin: "0px 5px" }}
                onClick={exportData}
              >
                Export Data
              </Button>
            )}
            <CSVLink className="hidden" ref={excelRef} data={allData} />
            {/* <Button
                className="btn-dashboard-icon textcolor"
                type="primary"
                // ghost
                icon={<FaDownload size={13} />}
                // size="large"
                style={{ margin: "0px 5px" }}
              // onClick={props.action}
              // {...props.ButtonDefault}
              >
                Export Data
              </Button>
            </CSVLink> */}
            {/* <CustomButton action={(event, done) => exportData(event, done)} data={allData} type="asynclinkicon" icon={<FaDownload size={13} />} name="Export Data" /> */}
          </div>
        </div>
        <div className="mt-3">
          <Row>
            <Col span={24} lg={12} xl={8} xxl={8}>
              <Search
                width="auto"
                value={searchValue}
                // defaultValue={filterText}
                onChange={(e) => {
                  searchHandler(e.target.value);
                  setSearchValue(e.target.value);
                }}
                placeholder="Search by Endpoint name"
              />
            </Col>
          </Row>
        </div>
        {selectedRowKeys?.length > 0 && (
          <>
            {' '}
            <div className="gap-4 flex">

              <Popconfirm
                placement="top"
                title="Are you sure you want to Block the API data?"
                description={(
                  <p>
                    Blocking the API data will make it invisible on the map. However, you will be able to
                    <br />
                    unblock it later from the Blocked API section if needed.
                  </p>
                )}
                onConfirm={blukVisibilityHandler}
              >
                <Button type="primary" className="mt-5 textcolor">
                  Select endpoints to block
                </Button>
              </Popconfirm>
              <Popconfirm
                placement="top"
                title="Are you sure you want to Block the all API data?"
                description={(
                  <p>
                    Blocking the API data will make it invisible on the map. However, you will be able to
                    <br />
                    unblock it later from the Blocked API section if needed.
                  </p>
                )}
                onConfirm={blukAllVisibilityHandler}
              >
                <Button type="primary" className="mt-5 textcolor">
                  {`Block all ${convertLocalStringWithoutRupee(totalEndpointCounts)} endpoints`}
                </Button>
              </Popconfirm>
            </div>
          </>
        )}
        <div className="mt-5">
          <Table
            loading={api.isLoading}
            dataSource={apiOverView}
            columns={headers}
            onChange={handleTableChange}
            pagination={false}
            scroll={{ x: 800, y: 1300 }}
            rowSelection={{
              type: "checkbox",
              ...rowSelection,
            }}
          />
          <Pagination
            current={pagination?.currentPage}
            pageSize={pagination?.pageSize}
            total={pagination?.total}
            className="mt-10"
            showSizeChanger
            onChange={(page, pageSize) => {
              setPagination((prev) => ({
                ...prev,
                currentPage: page,
                pageSize,
              }));
            }}
          />
        </div>
      </Card>
    </>
  );
};

export default EndpointUtilization;
