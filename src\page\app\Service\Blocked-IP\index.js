import React, { useState } from 'react';
import {
    Card, Row, Col, notification, Tabs,
} from 'antd';
import { useParams } from 'react-router-dom';
import { ShieldOutlined, HistoryOutlined } from '@ant-design/icons';
import CRUDComponent from '../../../../component/common/CRUD-Component';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import { rateLimitDummyAPI } from '../../../../util/dummyData/rateLimitData';

const { TabPane } = Tabs;

const BlockedIPPage = () => {
    const [refresh, setRefresh] = useState(false);
    const { environmentID } = useParams();

    // Dummy API functions for CRUD operations
    const dummyBlockedIPAPI = {
        type: "GET",
        endpoint: "/blockedIPs",
        getData: async (params) => {
            const response = await rateLimitDummyAPI.getBlockedIPs(environmentID, params);
            return response;
        },
    };

    const dummyRateLimitLogsAPI = {
        type: "GET",
        endpoint: "/rateLimitLogs",
        getData: async (params) => {
            const response = await rateLimitDummyAPI.getRateLimitLogs(environmentID, params);
            return response;
        },
    };

    const handleUnblockIP = async (ipId) => {
        try {
            const response = await rateLimitDummyAPI.unblockIP(ipId);
            if (response.success) {
                notification.success({
                    message: 'Success',
                    description: 'IP unblocked successfully',
                });
                setRefresh((prev) => !prev);
            }
        } catch (error) {
            notification.error({
                message: 'Error',
                description: 'Failed to unblock IP',
            });
        }
    };

    const handleBlockIP = async (values) => {
        try {
            const response = await rateLimitDummyAPI.blockIP({
                ...values,
                serviceEnvironmentId: environmentID,
            });
            if (response.success) {
                notification.success({
                    message: 'Success',
                    description: 'IP blocked successfully',
                });
                setRefresh((prev) => !prev);
            }
        } catch (error) {
            notification.error({
                message: 'Error',
                description: 'Failed to block IP',
            });
        }
    };

    return (
        <Row gutter={[16, 24]}>
            <Col span={24}>
                <Card
                    title={(
                        <div className="flex items-center gap-2">
                            <ShieldOutlined className="text-red-500" />
                            <span>IP Management</span>
                        </div>
                    )}
                >
                    <Tabs defaultActiveKey="1" type="card">
                        <TabPane
                            tab={(
                                <span>
                                    <ShieldOutlined />
                                    Blocked IPs
                                </span>
                            )}
                            key="1"
                        >
                            <CRUDComponent
                                reload={refresh}
                                GET={{
                                    API: dummyBlockedIPAPI,
                                    DataModifier: (res) => {
                                        return res?.map((el, i) => ({
                                            ...el,
                                            key: el?.id,
                                            no: i + 1,
                                            unblockAction: {
                                                id: el?.id,
                                                onClick: handleUnblockIP,
                                            },
                                        }));
                                    },
                                    column: CONSTANTS.TABLE.BLOCKED_IPS,
                                }}
                                CREATE={{
                                    API: { type: "POST", endpoint: "/blockedIPs" },
                                    message: "IP blocked successfully",
                                    modaltitle: "Block IP Address",
                                    modalFields: "BLOCK_IP_MODAL",
                                    payloadModifier: (payload) => ({
                                        ...payload,
                                        serviceEnvironmentId: environmentID,
                                    }),
                                    customHandler: handleBlockIP,
                                }}
                                DELETE={{
                                    API: { type: "DELETE", endpoint: "/blockedIPs" },
                                    message: "IP unblocked successfully",
                                    customHandler: handleUnblockIP,
                                }}
                            />
                        </TabPane>
                        <TabPane
                            tab={(
                                <span>
                                    <HistoryOutlined />
                                    Rate Limit Logs
                                </span>
                            )}
                            key="2"
                        >
                            <CRUDComponent
                                reload={refresh}
                                GET={{
                                    API: dummyRateLimitLogsAPI,
                                    DataModifier: (res) => {
                                        return res?.map((el, i) => ({
                                            ...el,
                                            key: el?.id,
                                            no: i + 1,
                                        }));
                                    },
                                    column: CONSTANTS.TABLE.RATE_LIMIT_LOGS,
                                }}
                                selectionOff
                            />
                        </TabPane>
                    </Tabs>
                </Card>
            </Col>
        </Row>
    );
};

export default BlockedIPPage;
