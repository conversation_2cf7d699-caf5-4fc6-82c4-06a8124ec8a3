import React from "react";

import { Column } from "@ant-design/plots";
import { each, groupBy } from "@antv/util";

function BarChart({ data = [] }) {
  const annotations = [];
  each(groupBy(data, "year"), (values, k) => {
    const value = values.reduce((a, b) => a + b.value, 0);
    annotations.push({
      type: "text",
      position: [k, value],
      content: `${value?.toFixed(2)}%`,
      style: {
        textAlign: "center",
        fontSize: 10,
        fill: "rgba(0,0,0,0.85)",
      },
      offsetY: -10,
    });
  });

  const config = {
    data,
    isStack: true,
    xField: "year",
    yField: "value",
    seriesField: "category",
    label: {
      position: "middle",
      layout: [
        {
          type: "interval-adjust-position",
        },
        {
          type: "interval-hide-overlap",
        },
        {
          type: "adjust-color",
        },
      ],
      style: {
        fontSize: 10,
      },
    },
    annotations,
  };

  return <Column {...config} />;
}
export default BarChart;
